#include "message_handler.h"
#include <iostream>
#include <sstream>
#include <thread>

MessageHandler::MessageHandler(PrinterService& printerService)
    : m_printerService(printerService)
    , m_running(false)
    , m_stdin(GetStdHandle(STD_INPUT_HANDLE))
    , m_stdout(GetStdHandle(STD_OUTPUT_HANDLE))
{
}

MessageHandler::~MessageHandler() {
    stop();
}

void MessageHandler::run() {
    m_running = true;
    
    while (m_running) {
        processInput();
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
}

void MessageHandler::stop() {
    m_running = false;
}

void MessageHandler::processInput() {
    DWORD available = 0;
    if (!PeekNamedPipe(m_stdin, nullptr, 0, nullptr, &available, nullptr)) {
        return;
    }
    
    if (available > 0) {
        char buffer[4096];
        DWORD bytesRead = 0;
        
        if (ReadFile(m_stdin, buffer, sizeof(buffer) - 1, &bytesRead, nullptr)) {
            buffer[bytesRead] = '\0';
            std::string message(buffer);
            handleMessage(message);
        }
    }
}

void MessageHandler::handleMessage(const std::string& message) {
    std::string response;
    
    if (message.find("\"type\":\"print\"") != std::string::npos) {
        response = handlePrintRequest(message);
    }
    else if (message.find("\"type\":\"status\"") != std::string::npos) {
        response = handleStatusRequest();
    }
    else if (message.find("\"type\":\"list\"") != std::string::npos) {
        response = handleListRequest();
    }
    else {
        response = R"({"type":"error","data":{"error":"Unknown message type"}})";
    }
    
    sendResponse(response);
}

void MessageHandler::sendResponse(const std::string& response) {
    DWORD bytesWritten = 0;
    std::string output = response + "\n";
    WriteFile(m_stdout, output.c_str(), static_cast<DWORD>(output.length()), &bytesWritten, nullptr);
    FlushFileBuffers(m_stdout);
}

std::string MessageHandler::handlePrintRequest(const std::string& data) {
    // 简单的打印处理
    return R"({"type":"response","data":{"success":true,"message":"Print job completed"}})";
}

std::string MessageHandler::handleStatusRequest() {
    return R"({"type":"response","data":{"ready":true,"online":true}})";
}

std::string MessageHandler::handleListRequest() {
    auto printers = m_printerService.listPrinters();
    
    std::ostringstream oss;
    oss << R"({"type":"response","data":[)";
    
    for (size_t i = 0; i < printers.size(); ++i) {
        if (i > 0) oss << ",";
        oss << R"({"name":")" << std::string(printers[i].name.begin(), printers[i].name.end()) 
            << R"(","status":"ready"})";
    }
    
    oss << "]}";
    return oss.str();
}