#pragma once

#include <windows.h>
#include <string>
#include <functional>
#include "printer_service.h"

class MessageHandler {
public:
    explicit MessageHandler(PrinterService& printerService);
    ~MessageHandler();
    
    void run();
    void stop();

private:
    PrinterService& m_printerService;
    bool m_running;
    HANDLE m_stdin;
    HANDLE m_stdout;
    
    void processInput();
    void handleMessage(const std::string& message);
    void sendResponse(const std::string& response);
    
    std::string handlePrintRequest(const std::string& data);
    std::string handleStatusRequest();
    std::string handleListRequest();
};