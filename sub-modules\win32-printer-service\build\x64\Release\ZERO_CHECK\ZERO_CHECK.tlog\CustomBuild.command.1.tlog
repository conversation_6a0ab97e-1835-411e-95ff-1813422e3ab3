^C:\USERS\<USER>\WORKSPACE\ABC-PRINT-SERVICE\SUB-MODULES\WIN32-PRINTER-SERVICE\BUILD\CMAKEFILES\AA12937F151201162F5A13C5EE66EB9D\GENERATE.STAMP.RULE
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/workspace/abc-print-service/sub-modules/win32-printer-service -BC:/Users/<USER>/workspace/abc-print-service/sub-modules/win32-printer-service/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/workspace/abc-print-service/sub-modules/win32-printer-service/build/Win32PrinterService.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
