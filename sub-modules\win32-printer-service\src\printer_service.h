#pragma once

#include <windows.h>
#include <winspool.h>
#include <string>
#include <vector>
#include <memory>

struct PrinterInfo {
    std::wstring name;
    std::wstring status;
    bool isDefault;
    bool isOnline;
};

struct PrintJob {
    std::wstring documentName;
    std::wstring printerName;
    std::vector<uint8_t> data;
    std::wstring dataType;
};

class PrinterService {
public:
    PrinterService();
    ~PrinterService();
    
    // 获取打印机列表
    std::vector<PrinterInfo> listPrinters();
    
    // 获取默认打印机
    std::wstring getDefaultPrinter();
    
    // 打印文档
    bool printDocument(const PrintJob& job);
    
    // 获取打印机状态
    PrinterInfo getPrinterStatus(const std::wstring& printerName);
    
    // 检查打印机是否在线
    bool isPrinterOnline(const std::wstring& printerName);

private:
    void initializePrinterService();
    std::wstring getLastErrorString();
};