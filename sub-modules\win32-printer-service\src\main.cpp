#include <windows.h>
#include <iostream>
#include <string>
#include <thread>
#include "printer_service.h"
#include "message_handler.h"

int main(int argc, char* argv[])
{
    // 初始化COM
    CoInitialize(nullptr);
    
    try {
        PrinterService printerService;
        MessageHandler messageHandler(printerService);
        // 主消息循环
        messageHandler.run();
        
    } catch (const std::exception& e) {
        std::cerr << "Error: " << e.what() << std::endl;
        CoUninitialize();
        return 1;
    }
    
    CoUninitialize();
    return 0;
}