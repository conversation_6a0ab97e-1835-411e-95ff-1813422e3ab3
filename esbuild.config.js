const esbuild = require('esbuild');
const { nodeExternalsPlugin } = require('esbuild-node-externals');
const fs = require('fs');
const path = require('path');

const isWatch = process.argv.includes('--watch');
const isProd = process.env.NODE_ENV === 'production';

console.log(`Building in ${isProd ? 'production' : 'development'} mode`);
console.log(`Watch mode: ${isWatch ? 'enabled' : 'disabled'}`);

// Function to recursively delete a directory
function deleteDirRecursive(dirPath) {
  if (fs.existsSync(dirPath)) {
    fs.readdirSync(dirPath).forEach((file) => {
      const curPath = path.join(dirPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        // Recursive call
        deleteDirRecursive(curPath);
      } else {
        // Delete file
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(dirPath);
  }
}

// Function to clean dist directory
function cleanDistDir() {
  const distDir = path.join(__dirname, 'dist');
  console.log('Cleaning dist directory...');
  
  if (fs.existsSync(distDir)) {
    deleteDirRecursive(distDir);
  }
  
  // Recreate the dist directory
  fs.mkdirSync(distDir, { recursive: true });
  
  console.log('Dist directory cleaned.');
}

// Function to copy directory recursively
function copyDirSync(src, dest, fileFilter = null) {
  if (!fs.existsSync(dest)) {
    fs.mkdirSync(dest, { recursive: true });
  }
  
  const entries = fs.readdirSync(src, { withFileTypes: true });
  
  for (const entry of entries) {
    const srcPath = path.join(src, entry.name);
    const destPath = path.join(dest, entry.name);
    
    if (entry.isDirectory()) {
      copyDirSync(srcPath, destPath, fileFilter);
    } else {
      // If a file filter is provided, check if the file should be copied
      if (!fileFilter || fileFilter(entry.name)) {
        fs.copyFileSync(srcPath, destPath);
      }
    }
  }
}

function copyNativeModule() {
  // const win32ToolsPath = path.join(__dirname, './dist/win32_tools.node');
  // if (!fs.existsSync(win32ToolsPath)) {
  //   fs.copyFileSync(path.join(__dirname, './sub-modules/win32-tools/bin/win32-ia32-110/win32-tools.node'), win32ToolsPath);
  // }

  // const win32RpaPath = path.join(__dirname, './dist/win32_rpa.node');
  // if (!fs.existsSync(win32RpaPath)) {
  //   fs.copyFileSync(path.join(__dirname, './sub-modules/win32-rpa/build/Release/win32_rpa.node'), win32RpaPath);
  // }
}

const buildOptions = {
  entryPoints: ['./src/main.ts'],
  bundle: true,
  platform: 'node',
  target: 'node14',
  outfile: './dist/main.js',
  sourcemap: !isProd,
  minify: isProd,
  plugins: [
    // Don't bundle node_modules
    nodeExternalsPlugin({
      // Add any dependencies you want to bundle
      allowList: [],
    }),
  ],
  // Mark native modules as external
  external: ['*.node']
};

// Clean dist directory before build
cleanDistDir();

if (isWatch) {
  // Watch mode
  esbuild.context(buildOptions).then(context => {
    context.watch();
    console.log('Watching for changes...');
    copyNativeModule();
  });
} else {
  // Build once
  esbuild.build(buildOptions).then(() => {
    console.log('Build complete');
    copyNativeModule();
  }).catch((error) => {
    console.error('Build failed:', error);
    process.exit(1);
  });
}
