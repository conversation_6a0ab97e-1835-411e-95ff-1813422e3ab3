#include "printer_service.h"
#include <iostream>
#include <sstream>

PrinterService::PrinterService() {
    initializePrinterService();
}

PrinterService::~PrinterService() {
    // 清理资源
}

void PrinterService::initializePrinterService() {
    // 初始化打印服务
    std::wcout << L"Initializing printer service..." << std::endl;
}

std::vector<PrinterInfo> PrinterService::listPrinters() {
    std::vector<PrinterInfo> printers;
    
    DWORD needed = 0;
    DWORD returned = 0;
    
    // 获取所需缓冲区大小
    EnumPrinters(PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS, 
                 nullptr, 2, nullptr, 0, &needed, &returned);
    
    if (needed > 0) {
        auto buffer = std::make_unique<BYTE[]>(needed);
        PRINTER_INFO_2* printerInfo = reinterpret_cast<PRINTER_INFO_2*>(buffer.get());
        
        if (EnumPrinters(PRINTER_ENUM_LOCAL | PRINTER_ENUM_CONNECTIONS,
                        nullptr, 2, buffer.get(), needed, &needed, &returned)) {
            
            for (DWORD i = 0; i < returned; i++) {
                PrinterInfo info;
                info.name = printerInfo[i].pPrinterName ? printerInfo[i].pPrinterName : L"";
                info.status = L"Ready";
                info.isDefault = false;
                info.isOnline = true;
                
                printers.push_back(info);
            }
        }
    }
    
    return printers;
}

std::wstring PrinterService::getDefaultPrinter() {
    DWORD size = 0;
    GetDefaultPrinter(nullptr, &size);
    
    if (size > 0) {
        auto buffer = std::make_unique<wchar_t[]>(size);
        if (GetDefaultPrinter(buffer.get(), &size)) {
            return std::wstring(buffer.get());
        }
    }
    
    return L"";
}

bool PrinterService::printDocument(const PrintJob& job) {
    HANDLE hPrinter = nullptr;
    
    if (!OpenPrinter(const_cast<LPWSTR>(job.printerName.c_str()), &hPrinter, nullptr)) {
        std::wcerr << L"Failed to open printer: " << job.printerName << std::endl;
        return false;
    }
    
    DOC_INFO_1 docInfo = {};
    docInfo.pDocName = const_cast<LPWSTR>(job.documentName.c_str());
    docInfo.pOutputFile = nullptr;
    docInfo.pDatatype = const_cast<LPWSTR>(job.dataType.c_str());
    
    DWORD jobId = StartDocPrinter(hPrinter, 1, reinterpret_cast<LPBYTE>(&docInfo));
    if (jobId == 0) {
        std::wcerr << L"Failed to start document" << std::endl;
        ClosePrinter(hPrinter);
        return false;
    }
    
    if (!StartPagePrinter(hPrinter)) {
        std::wcerr << L"Failed to start page" << std::endl;
        EndDocPrinter(hPrinter);
        ClosePrinter(hPrinter);
        return false;
    }
    
    DWORD bytesWritten = 0;
    bool success = WritePrinter(hPrinter, 
                               const_cast<LPVOID>(static_cast<const void*>(job.data.data())),
                               static_cast<DWORD>(job.data.size()),
                               &bytesWritten);
    
    EndPagePrinter(hPrinter);
    EndDocPrinter(hPrinter);
    ClosePrinter(hPrinter);
    
    return success && (bytesWritten == job.data.size());
}

PrinterInfo PrinterService::getPrinterStatus(const std::wstring& printerName) {
    PrinterInfo info;
    info.name = printerName;
    info.status = L"Ready";
    info.isDefault = (printerName == getDefaultPrinter());
    info.isOnline = isPrinterOnline(printerName);
    
    return info;
}

bool PrinterService::isPrinterOnline(const std::wstring& printerName) {
    HANDLE hPrinter = nullptr;
    
    if (!OpenPrinter(const_cast<LPWSTR>(printerName.c_str()), &hPrinter, nullptr)) {
        return false;
    }
    
    DWORD needed = 0;
    GetPrinter(hPrinter, 2, nullptr, 0, &needed);
    
    if (needed > 0) {
        auto buffer = std::make_unique<BYTE[]>(needed);
        PRINTER_INFO_2* printerInfo = reinterpret_cast<PRINTER_INFO_2*>(buffer.get());
        
        if (GetPrinter(hPrinter, 2, buffer.get(), needed, &needed)) {
            bool online = !(printerInfo->Status & PRINTER_STATUS_OFFLINE);
            ClosePrinter(hPrinter);
            return online;
        }
    }
    
    ClosePrinter(hPrinter);
    return false;
}

std::wstring PrinterService::getLastErrorString() {
    DWORD error = GetLastError();
    LPWSTR messageBuffer = nullptr;
    
    FormatMessage(FORMAT_MESSAGE_ALLOCATE_BUFFER | FORMAT_MESSAGE_FROM_SYSTEM,
                  nullptr, error, 0, reinterpret_cast<LPWSTR>(&messageBuffer), 0, nullptr);
    
    std::wstring message = messageBuffer ? messageBuffer : L"Unknown error";
    LocalFree(messageBuffer);
    
    return message;
}