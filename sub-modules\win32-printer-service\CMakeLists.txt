cmake_minimum_required(VERSION 3.16)
project(Win32PrinterService)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置输出目录
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

# 添加可执行文件
add_executable(Win32PrinterService
    src/main.cpp
    src/printer_service.cpp
    src/printer_service.h
    src/message_handler.cpp
    src/message_handler.h
)

# 链接Windows库
target_link_libraries(Win32PrinterService
    winspool
    user32
    kernel32
)

# 设置编译选项
if(MSVC)
    target_compile_options(Win32PrinterService PRIVATE /W4)
else()
    target_compile_options(Win32PrinterService PRIVATE -Wall -Wextra -Wpedantic)
endif()